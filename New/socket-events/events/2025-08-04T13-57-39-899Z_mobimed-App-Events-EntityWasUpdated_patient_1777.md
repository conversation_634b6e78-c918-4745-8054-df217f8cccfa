# mobimed:App\Events\EntityWasUpdated

## Event Information

- **Event Type**: mobimed:App\Events\EntityWasUpdated
- **Model**: patient
- **Event ID**: 1777
- **Timestamp**: 2025-08-04T13:57:39.899Z
- **Webhook Event**: EntityWasUpdated
- **Webhook Model**: Patient

## Original Socket Event Data

```json
{
  "type": "patient",
  "payload": {
    "id": 1777,
    "createdAt": "2025-07-30T21:35:13.000Z",
    "updatedAt": "2025-07-30T22:05:39.000Z",
    "createdBy": 5003,
    "updatedBy": 5003,
    "firstName": "AP",
    "lastName": "CC",
    "dob": null,
    "ssn": null,
    "flashMessage": "",
    "active": true,
    "phoneMobile": null,
    "phonePersonal": null,
    "phoneBusiness": null,
    "email": null,
    "title": "Mr.",
    "titleSuffix": null,
    "healthInsurance": "SVS-GW",
    "gender": null,
    "addresses": [
      {
        "id": 1796,
        "label": null,
        "name": null,
        "street": null,
        "streetNumber": null,
        "postalCode": null,
        "city": null,
        "country": "US",
        "primary": 1
      }
    ],
    "categories": [],
    "customFields": [
      12206,
      12218,
      12207,
      12208,
      12209,
      12217,
      12210,
      12211,
      12212,
      12213,
      12214,
      12215,
      12216
    ],
    "invoices": [],
    "payments": [],
    "files": [],
    "history": [],
    "appointments": [],
    "messages": [],
    "medications": [],
    "personalWebForms": [],
    "qrUrl": "https://api-ccdemo.clinicore.eu/v1/qr/100/1777.png",
    "avatarUrl": null
  },
  "socket": null
}
```

## Converted Webhook Data

```json
{
  "event": "EntityWasUpdated",
  "model": "Patient",
  "id": 1777,
  "payload": {
    "id": 1777,
    "createdAt": "2025-07-30T21:35:13.000Z",
    "updatedAt": "2025-07-30T22:05:39.000Z",
    "createdBy": 5003,
    "updatedBy": 5003,
    "firstName": "AP",
    "lastName": "CC",
    "dob": null,
    "ssn": null,
    "flashMessage": "",
    "active": true,
    "phoneMobile": null,
    "phonePersonal": null,
    "phoneBusiness": null,
    "email": null,
    "title": "Mr.",
    "titleSuffix": null,
    "healthInsurance": "SVS-GW",
    "gender": null,
    "addresses": [
      {
        "id": 1796,
        "label": null,
        "name": null,
        "street": null,
        "streetNumber": null,
        "postalCode": null,
        "city": null,
        "country": "US",
        "primary": 1
      }
    ],
    "categories": [],
    "customFields": [
      12206,
      12218,
      12207,
      12208,
      12209,
      12217,
      12210,
      12211,
      12212,
      12213,
      12214,
      12215,
      12216
    ],
    "invoices": [],
    "payments": [],
    "files": [],
    "history": [],
    "appointments": [],
    "messages": [],
    "medications": [],
    "personalWebForms": [],
    "qrUrl": "https://api-ccdemo.clinicore.eu/v1/qr/100/1777.png",
    "avatarUrl": null
  },
  "timestamp": "2025-08-04T13:57:39.899Z"
}
```

## Event Processing

This event was received from the CC socket server and converted to webhook format for processing by the DermaCare sync system.

### Processing Flow
1. **Socket Event Received**: mobimed:App\Events\EntityWasUpdated
2. **Data Extraction**: Extracted model type and payload data
3. **Webhook Conversion**: Converted to webhook format compatible with ccHandler.ts
4. **Webhook Transmission**: Sent to dev server at http://localhost:8787/webhooks/cc

---
*Generated by DermaCare Socket Event Logger*
