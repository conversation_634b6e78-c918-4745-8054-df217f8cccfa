/**
 * Patient Custom Field Value Converters
 *
 * Handles value conversion between AutoPatient and CliniCore custom field formats.
 * Provides intelligent type-aware conversion with proper error handling and validation.
 *
 * @fileoverview Value conversion utilities for patient custom field synchronization
 * @version 1.0.0
 * @since 2024-07-29
 */

import { convertFromTextboxListValue } from "@/processors/customFields/config/textboxListHandling";
import type { GetCCCustomField } from "@/type";
import { logWarn, logDebug } from "@/utils/logger";
import type {
	APCustomFieldValue,
	CCCustomFieldValue,
	FieldMapping,
	ValueConversionResult,
} from "./types";

/**
 * Convert AutoPatient custom field value to CliniCore format
 *
 * @param apValue - AP custom field value
 * @param mapping - Field mapping from database
 * @returns Converted CC custom field value
 */
export function convertApValueToCc(
	apValue: APCustomFieldValue,
	mapping: FieldMapping,
): ValueConversionResult {
	try {
		const { apConfig, ccConfig } = mapping;
		const sourceValue = apValue.value;

		// Handle null/undefined values
		if (
			sourceValue === null ||
			sourceValue === undefined ||
			sourceValue === ""
		) {
			return {
				success: true,
				convertedValue: null,
				originalValue: sourceValue,
			};
		}

		const ccValue: CCCustomFieldValue = {
			field: ccConfig,
			values: [],
			patient: null,
		};

		// Special handling for AP TEXTBOX_LIST → CC multi-value fields (all compatible types)
		const isTextboxListToMultiValue =
			apConfig.dataType === "TEXTBOX_LIST" &&
			ccConfig.allowMultipleValues &&
			["text", "textarea", "email", "telephone", "number"].includes(
				ccConfig.type,
			);

		if (isTextboxListToMultiValue) {
			// Use the existing textboxListHandling function for consistent conversion
			const convertedValues = convertFromTextboxListValue(
				sourceValue,
				apConfig,
				ccConfig,
			);

			// convertFromTextboxListValue returns string[] for multi-value fields
			if (Array.isArray(convertedValues)) {
				ccValue.values = convertedValues.map((value) => ({ value }));
			} else {
				ccValue.values = [{ value: convertedValues }];
			}

			return {
				success: true,
				convertedValue: ccValue,
				originalValue: sourceValue,
			};
		}

		// Convert based on CC field type for non-TEXTBOX_LIST cases
		switch (ccConfig.type) {
			case "text":
			case "textarea":
			case "email":
			case "telephone":
				ccValue.values = [{ value: String(sourceValue) }];
				break;

			case "number": {
				const numValue = Number(sourceValue);
				if (Number.isNaN(numValue)) {
					return {
						success: false,
						convertedValue: null,
						originalValue: sourceValue,
						error: `Invalid number value: ${sourceValue}`,
					};
				}
				ccValue.values = [{ value: String(numValue) }];
				break;
			}

			case "date": {
				// Date fields don't support arrays, use first value if array
				const dateInput = Array.isArray(sourceValue)
					? sourceValue[0]
					: sourceValue;
				const dateValue = convertToDateString(dateInput);
				if (!dateValue) {
					return {
						success: false,
						convertedValue: null,
						originalValue: sourceValue,
						error: `Invalid date value: ${sourceValue}`,
					};
				}
				ccValue.values = [{ value: dateValue }];
				break;
			}

			case "boolean": {
				// Boolean fields don't support arrays, use first value if array
				const boolInput = Array.isArray(sourceValue)
					? sourceValue[0]
					: sourceValue;
				const boolValue = convertToBooleanString(boolInput);
				ccValue.values = [{ value: boolValue }];
				break;
			}

			case "select":
			case "select-or-custom": {
				// Select fields don't support arrays, use first value if array
				const selectInput = Array.isArray(sourceValue)
					? sourceValue[0]
					: sourceValue;
				return convertApSelectValueToCc(selectInput, ccConfig, ccValue);
			}

			default:
				logWarn("Unknown CC field type, using text conversion", {
					ccFieldType: ccConfig.type,
					ccFieldId: ccConfig.id,
				});
				ccValue.values = [{ value: String(sourceValue) }];
		}

		return {
			success: true,
			convertedValue: ccValue,
			originalValue: sourceValue,
		};
	} catch (error) {
		return {
			success: false,
			convertedValue: null,
			originalValue: apValue.value,
			error: `Conversion error: ${String(error)}`,
		};
	}
}

/**
 * Convert CliniCore custom field value to AutoPatient format
 *
 * @param ccValue - CC custom field value
 * @param mapping - Field mapping from database
 * @returns Converted AP custom field value
 */
export function convertCcValueToAp(
	ccValue: CCCustomFieldValue,
	mapping: FieldMapping,
): ValueConversionResult {
	try {
		const { apConfig, ccConfig } = mapping;
		const sourceValues = ccValue.values;

		// Handle empty values
		if (!sourceValues || sourceValues.length === 0) {
			return {
				success: true,
				convertedValue: null,
				originalValue: sourceValues,
			};
		}

		const apValue: APCustomFieldValue = {
			id: apConfig.id,
			value: null,
		};

		// Special handling for CC multi-value fields → AP TEXTBOX_LIST
		const isCcMultiValueToTextboxList =
			ccConfig.allowMultipleValues &&
			apConfig.dataType === "TEXTBOX_LIST" &&
			["text", "textarea", "email", "telephone", "number", "date"].includes(
				ccConfig.type,
			);

		if (isCcMultiValueToTextboxList) {
			const values = sourceValues
				.map((v) => v.value)
				.filter(Boolean) as string[];

			// Mark this field for special TEXTBOX_LIST processing
			// The actual field_value mapping will be done by the async processor
			apValue.value = `__TEXTBOX_LIST_VALUES__:${JSON.stringify(values)}`;

			return {
				success: true,
				convertedValue: apValue,
				originalValue: sourceValues,
			};
		}

		// Convert based on AP field type
		switch (apConfig.dataType) {
			case "TEXT":
			case "LARGE_TEXT":
			case "EMAIL":
			case "PHONE":
			case "MONETORY":
				apValue.value = sourceValues[0]?.value || "";
				break;

			case "NUMERICAL": {
				const numStr = sourceValues[0]?.value || "0";
				const numValue = Number(numStr);
				if (Number.isNaN(numValue)) {
					return {
						success: false,
						convertedValue: null,
						originalValue: sourceValues,
						error: `Invalid number value: ${numStr}`,
					};
				}
				apValue.value = numValue;
				break;
			}

			case "DATE": {
				const dateStr = sourceValues[0]?.value || "";
				apValue.value = dateStr;
				break;
			}

			case "RADIO":
			case "SINGLE_OPTIONS":
				apValue.value = sourceValues[0]?.value || "";
				break;

			case "CHECKBOX":
			case "MULTIPLE_OPTIONS":
			case "TEXTBOX_LIST": {
				// Handle multiple values
				const values = sourceValues.map((v) => v.value).filter(Boolean);
				apValue.value = values.join(", ");
				break;
			}

			default:
				logWarn("Unknown AP field type, using text conversion", {
					apFieldType: apConfig.dataType,
					apFieldId: apConfig.id,
				});
				apValue.value = sourceValues[0]?.value || "";
		}

		return {
			success: true,
			convertedValue: apValue,
			originalValue: sourceValues,
		};
	} catch (error) {
		return {
			success: false,
			convertedValue: null,
			originalValue: ccValue.values,
			error: `Conversion error: ${String(error)}`,
		};
	}
}

/**
 * Convert AP select value to CC format with allowed values handling
 */
function convertApSelectValueToCc(
	sourceValue: string | number | boolean | null,
	ccConfig: GetCCCustomField,
	ccValue: CCCustomFieldValue,
): ValueConversionResult {
	const valueStr = String(sourceValue);

	// Handle multiple values (comma-separated)
	const values = valueStr
		.split(",")
		.map((v) => v.trim())
		.filter(Boolean);

	for (const value of values) {
		// Try to find matching allowed value
		const allowedValue = ccConfig.allowedValues?.find(
			(av) => av.value === value,
		);

		if (allowedValue) {
			// Use predefined option ID
			ccValue.values.push({ id: allowedValue.id });
		} else if (ccConfig.type === "select-or-custom") {
			// Allow custom value for select-or-custom fields
			ccValue.values.push({ value });
		} else {
			// For strict select fields, use the value as-is (may cause validation error)
			ccValue.values.push({ value });
		}
	}

	return {
		success: true,
		convertedValue: ccValue,
		originalValue: sourceValue,
		warnings:
			ccConfig.allowedValues?.length > 0 &&
			!ccConfig.allowedValues.some((av) => values.includes(av.value))
				? [
						`Value "${valueStr}" not found in allowed values for field "${ccConfig.name}"`,
					]
				: undefined,
	};
}

/**
 * Convert value to date string format
 */
function convertToDateString(
	value: string | number | boolean | null,
): string | null {
	if (!value) return null;

	try {
		const date = new Date(String(value));
		if (Number.isNaN(date.getTime())) return null;

		// Return ISO date string (YYYY-MM-DD)
		return date.toISOString().split("T")[0];
	} catch {
		return null;
	}
}

/**
 * Convert value to boolean string representation
 */
function convertToBooleanString(
	value: string | number | boolean | null,
): string {
	if (typeof value === "boolean") {
		return value ? "true" : "false";
	}

	const str = String(value).toLowerCase().trim();

	// Handle common boolean representations
	if (["true", "1", "yes", "ja", "on", "enabled"].includes(str)) {
		return "true";
	}

	if (["false", "0", "no", "nein", "off", "disabled"].includes(str)) {
		return "false";
	}

	// Default to false for unknown values
	return "false";
}

/**
 * Convert AutoPatient standard field value to CliniCore custom field format
 *
 * Handles conversion of AP standard field values (email, phone, etc.) to CC custom field format.
 * This is used for standard-to-custom field mappings where AP standard fields need to be
 * synchronized to CC custom fields.
 *
 * @param apStandardFieldName - Name of the AP standard field (e.g., "email", "phone")
 * @param apStandardValue - Value from the AP standard field
 * @param ccField - Target CC custom field configuration
 * @returns Converted CC custom field value
 *
 * @example
 * ```typescript
 * const result = convertApStandardValueToCc(
 *   "email",
 *   "<EMAIL>",
 *   ccEmailField
 * );
 * if (result.success) {
 *   console.log("Converted email value:", result.convertedValue);
 * }
 * ```
 *
 * @since 2.0.0
 */
export function convertApStandardValueToCc(
	apStandardFieldName: string,
	apStandardValue: unknown,
	ccField: GetCCCustomField,
): ValueConversionResult {
	try {
		logDebug("Converting AP standard field value to CC custom field", {
			apStandardFieldName,
			apStandardValue,
			ccFieldId: ccField.id,
			ccFieldType: ccField.type,
		});

		// Handle null/undefined/empty values
		if (
			apStandardValue === null ||
			apStandardValue === undefined ||
			apStandardValue === ""
		) {
			return {
				success: true,
				convertedValue: null,
				originalValue: apStandardValue,
			};
		}

		const ccValue: CCCustomFieldValue = {
			field: ccField,
			values: [],
			patient: null,
		};

		// Convert based on CC field type and AP standard field type
		switch (ccField.type) {
			case "text":
			case "textarea":
				ccValue.values = [{ value: String(apStandardValue) }];
				break;

			case "email": {
				// Validate email format for email fields
				const emailStr = String(apStandardValue);
				if (apStandardFieldName === "email" && !isValidEmail(emailStr)) {
					return {
						success: false,
						convertedValue: null,
						originalValue: apStandardValue,
						error: `Invalid email format: ${emailStr}`,
						warnings: [`Email validation failed for value: ${emailStr}`],
					};
				}
				ccValue.values = [{ value: emailStr }];
				break;
			}

			case "telephone": {
				// Clean and validate phone numbers
				const phoneStr = String(apStandardValue);
				const cleanedPhone = cleanPhoneNumber(phoneStr);
				ccValue.values = [{ value: cleanedPhone }];
				break;
			}

			case "number": {
				// Handle numeric conversions for fields like age, etc.
				const numValue = Number(apStandardValue);
				if (Number.isNaN(numValue)) {
					return {
						success: false,
						convertedValue: null,
						originalValue: apStandardValue,
						error: `Invalid number value: ${apStandardValue}`,
					};
				}
				ccValue.values = [{ value: String(numValue) }];
				break;
			}

			case "date": {
				// Handle date fields (dateOfBirth, etc.)
				const dateValue = convertToDateString(apStandardValue as string | number | boolean | null);
				if (!dateValue) {
					return {
						success: false,
						convertedValue: null,
						originalValue: apStandardValue,
						error: `Invalid date value: ${apStandardValue}`,
					};
				}
				ccValue.values = [{ value: dateValue }];
				break;
			}

			case "boolean": {
				// Handle boolean fields (gender, etc.)
				const boolValue = convertToBooleanString(apStandardValue as string | number | boolean | null);
				ccValue.values = [{ value: boolValue }];
				break;
			}

			case "select":
			case "select-or-custom": {
				// Handle select fields with validation against allowed values
				const selectValue = String(apStandardValue);
				return convertApStandardSelectValueToCc(selectValue, ccField, ccValue);
			}

			default:
				logWarn("Unknown CC field type for standard field conversion, using text", {
					ccFieldType: ccField.type,
					ccFieldId: ccField.id,
					apStandardFieldName,
				});
				ccValue.values = [{ value: String(apStandardValue) }];
		}

		logDebug("Successfully converted AP standard field value", {
			apStandardFieldName,
			ccFieldId: ccField.id,
			originalValue: apStandardValue,
			convertedValueCount: ccValue.values.length,
		});

		return {
			success: true,
			convertedValue: ccValue,
			originalValue: apStandardValue,
		};
	} catch (error) {
		const errorMessage = error instanceof Error ? error.message : String(error);
		logWarn("Error converting AP standard field value to CC custom field", {
			apStandardFieldName,
			ccFieldId: ccField.id,
			error: errorMessage,
		});

		return {
			success: false,
			convertedValue: null,
			originalValue: apStandardValue,
			error: `Conversion error: ${errorMessage}`,
		};
	}
}

/**
 * Convert AP standard select value to CC format with allowed values handling
 */
function convertApStandardSelectValueToCc(
	sourceValue: string,
	ccField: GetCCCustomField,
	ccValue: CCCustomFieldValue,
): ValueConversionResult {
	// Try to find matching allowed value
	const allowedValue = ccField.allowedValues?.find(
		(av) => av.value === sourceValue,
	);

	if (allowedValue) {
		// Use predefined option ID
		ccValue.values.push({ id: allowedValue.id });
	} else if (ccField.type === "select-or-custom") {
		// Allow custom value for select-or-custom fields
		ccValue.values.push({ value: sourceValue });
	} else {
		// For strict select fields, use the value as-is (may cause validation error)
		ccValue.values.push({ value: sourceValue });
	}

	return {
		success: true,
		convertedValue: ccValue,
		originalValue: sourceValue,
		warnings:
			ccField.allowedValues?.length > 0 &&
			!ccField.allowedValues.some((av) => av.value === sourceValue)
				? [
						`Value "${sourceValue}" not found in allowed values for field "${ccField.name}"`,
					]
				: undefined,
	};
}

/**
 * Validate email format
 */
function isValidEmail(email: string): boolean {
	const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
	return emailRegex.test(email);
}

/**
 * Clean phone number by removing non-numeric characters except + and spaces
 */
function cleanPhoneNumber(phone: string): string {
	// Keep only digits, +, spaces, hyphens, and parentheses
	return phone.replace(/[^\d+\s\-()]/g, "").trim();
}
