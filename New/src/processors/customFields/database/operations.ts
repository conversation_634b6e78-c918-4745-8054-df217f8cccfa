/**
 * Custom Fields Database Operations
 *
 * Provides database operations for custom field synchronization between
 * AutoPatient (AP) and CliniCore (CC) platforms. Handles field mapping
 * storage, updates, and upsert operations with comprehensive error handling
 * and logging for data integrity and traceability.
 *
 * @fileoverview Database operations for custom field synchronization
 * @version 2.0.0
 * @since 2024-07-28
 */

import { dbSchema, getDb } from "@database";
import type { APGetCustomFieldType, GetCCCustomField } from "@type";
import { eq, isNull, and } from "drizzle-orm";
import type { StandardFieldMapping, StandardToCustomMapping } from "@/config/standardFieldMappings";
import { logDebug, logError } from "@/utils/logger";
import type { CustomFieldInsert } from "../types";

/**
 * Store mapping for newly created field pairs
 *
 * Creates a database record linking an AutoPatient custom field with its
 * corresponding CliniCore custom field. This mapping enables bidirectional
 * synchronization and prevents duplicate field creation in future operations.
 *
 * The function uses upsert logic to handle cases where a mapping might
 * already exist, ensuring data consistency and preventing constraint violations.
 *
 * @param apField - AutoPatient custom field that was created or matched
 * @param ccField - CliniCore custom field that was created or matched
 * @returns Promise that resolves when mapping is successfully stored
 *
 * @throws {Error} When database operation fails or field data is invalid
 *
 * @example
 * ```typescript
 * const apField: APGetCustomFieldType = {
 *   id: 123,
 *   name: "patient_notes",
 *   dataType: "TEXTAREA"
 * };
 *
 * const ccField: GetCCCustomField = {
 *   id: 456,
 *   name: "patient_notes",
 *   label: "Patient Notes",
 *   type: "TEXTAREA"
 * };
 *
 * await storeMappingForCreatedFields(apField, ccField);
 * console.log("Field mapping stored successfully");
 * ```
 *
 * @since 2.0.0
 */
export async function storeMappingForCreatedFields(
	apField: APGetCustomFieldType,
	ccField: GetCCCustomField,
): Promise<void> {
	try {
		const db = getDb();
		const mappingData: CustomFieldInsert = {
			apId: apField.id,
			ccId: ccField.id,
			name: apField.name,
			label: ccField.label,
			type: ccField.type,
			apConfig: apField,
			ccConfig: ccField,
			mappingType: "custom_to_custom",
			apStandardField: null,
			ccStandardField: null,
		};

		await db
			.insert(dbSchema.customFields)
			.values(mappingData)
			.onConflictDoUpdate({
				target: [dbSchema.customFields.apId],
				set: {
					ccId: mappingData.ccId,
					name: mappingData.name,
					label: mappingData.label,
					type: mappingData.type,
					apConfig: mappingData.apConfig,
					ccConfig: mappingData.ccConfig,
					mappingType: mappingData.mappingType,
					apStandardField: mappingData.apStandardField,
					ccStandardField: mappingData.ccStandardField,
					updatedAt: new Date(),
				},
			});

		logDebug("Stored mapping for newly created field pair", {
			apFieldId: apField.id,
			ccFieldId: ccField.id,
		});
	} catch (error) {
		const errorMessage = error instanceof Error ? error.message : String(error);
		logError("Failed to store field mapping", {
			apFieldId: apField.id,
			ccFieldId: ccField.id,
			error: errorMessage,
		});
		throw new Error(`Failed to store field mapping: ${errorMessage}`);
	}
}

/**
 * Store standard field mapping
 *
 * Creates a database record for a custom field that maps to a standard field
 * on the target platform. This prevents future attempts to create duplicate
 * custom fields for standard field mappings.
 *
 * @param customField - Custom field that maps to a standard field
 * @param standardFieldMapping - Standard field mapping configuration
 * @returns Promise that resolves when mapping is successfully stored
 *
 * @throws {Error} When database operation fails or mapping data is invalid
 *
 * @example
 * ```typescript
 * const mapping: StandardFieldMapping = {
 *   sourceField: "email_address",
 *   targetField: "email",
 *   sourcePlatform: "ap",
 *   targetPlatform: "cc"
 * };
 *
 * await storeStandardFieldMapping(apField, mapping);
 * console.log("Standard field mapping stored");
 * ```
 *
 * @since 2.0.0
 */
export async function storeStandardFieldMapping(
	customField: APGetCustomFieldType | GetCCCustomField,
	standardFieldMapping: StandardFieldMapping,
): Promise<void> {
	try {
		const db = getDb();
		const isApCustomField = "dataType" in customField;

		const mappingData: CustomFieldInsert = {
			apId: isApCustomField ? customField.id : null,
			ccId: !isApCustomField ? customField.id : null,
			name: customField.name,
			label: isApCustomField
				? customField.name
				: (customField as GetCCCustomField).label,
			type: isApCustomField
				? customField.dataType
				: (customField as GetCCCustomField).type,
			apConfig: isApCustomField ? customField : null,
			ccConfig: !isApCustomField ? customField : null,
			mappingType:
				standardFieldMapping.sourcePlatform === "ap"
					? "custom_to_standard"
					: "standard_to_custom",
			apStandardField:
				standardFieldMapping.targetPlatform === "ap"
					? standardFieldMapping.targetField
					: null,
			ccStandardField:
				standardFieldMapping.targetPlatform === "cc"
					? standardFieldMapping.targetField
					: null,
		};

		await db
			.insert(dbSchema.customFields)
			.values(mappingData)
			.onConflictDoUpdate({
				target: isApCustomField
					? [dbSchema.customFields.apId]
					: [dbSchema.customFields.ccId],
				set: {
					apId: mappingData.apId,
					ccId: mappingData.ccId,
					name: mappingData.name,
					label: mappingData.label,
					type: mappingData.type,
					apConfig: mappingData.apConfig,
					ccConfig: mappingData.ccConfig,
					mappingType: mappingData.mappingType,
					apStandardField: mappingData.apStandardField,
					ccStandardField: mappingData.ccStandardField,
					updatedAt: new Date(),
				},
			});

		logDebug("Stored standard field mapping", {
			customFieldId: customField.id,
			customFieldName: customField.name,
			standardField: standardFieldMapping.targetField,
			mappingType: mappingData.mappingType,
		});
	} catch (error) {
		const errorMessage = error instanceof Error ? error.message : String(error);
		logError("Failed to store standard field mapping", {
			customFieldId: customField.id,
			standardField: standardFieldMapping.targetField,
			error: errorMessage,
		});
		throw new Error(`Failed to store standard field mapping: ${errorMessage}`);
	}
}

/**
 * Update existing field mapping
 *
 * Updates an existing field mapping record with new information.
 * Used when field configurations change or additional mapping
 * information becomes available.
 *
 * @param mappingId - ID of the mapping record to update
 * @param updateData - Partial mapping data to update
 * @returns Promise that resolves when mapping is successfully updated
 *
 * @throws {Error} When database operation fails or mapping not found
 *
 * @example
 * ```typescript
 * await updateFieldMapping("mapping-123", {
 *   label: "Updated Label",
 *   type: "textarea"
 * }, "req-123");
 * ```
 *
 * @since 2.0.0
 */
export async function updateFieldMapping(
	mappingId: string,
	updateData: Partial<CustomFieldInsert>,
): Promise<void> {
	try {
		const db = getDb();

		await db
			.update(dbSchema.customFields)
			.set({
				...updateData,
				updatedAt: new Date(),
			})
			.where(eq(dbSchema.customFields.id, mappingId));

		logDebug("Updated field mapping", {
			mappingId,
			updateFields: Object.keys(updateData),
		});
	} catch (error) {
		const errorMessage = error instanceof Error ? error.message : String(error);
		logError("Failed to update field mapping", {
			mappingId,
			error: errorMessage,
		});
		throw new Error(`Failed to update field mapping: ${errorMessage}`);
	}
}

/**
 * Bulk upsert field mappings for improved performance
 *
 * Performs bulk upsert operations for multiple field mappings to improve
 * performance when processing large numbers of field relationships.
 * This function is optimized for batch operations during full synchronization.
 *
 * @param mappings - Array of field mapping data to upsert
 * @returns Promise that resolves when all mappings are successfully processed
 *
 * @throws {Error} When bulk operation fails or data validation errors occur
 *
 * @example
 * ```typescript
 * const mappings: CustomFieldInsert[] = [
 *   { apId: "1", ccId: 101, name: "field1", ... },
 *   { apId: "2", ccId: 102, name: "field2", ... }
 * ];
 *
 * await bulkUpsertFieldMappings(mappings);
 * console.log("Bulk mappings completed");
 * ```
 *
 * @since 2.0.0
 */
export async function bulkUpsertFieldMappings(
	mappings: CustomFieldInsert[],
): Promise<void> {
	if (mappings.length === 0) {
		logDebug("No mappings to process in bulk operation");
		return;
	}

	try {
		const db = getDb();

		// Process mappings in batches to avoid overwhelming the database
		const batchSize = 50;
		const batches = [];

		for (let i = 0; i < mappings.length; i += batchSize) {
			batches.push(mappings.slice(i, i + batchSize));
		}

		for (const batch of batches) {
			await db
				.insert(dbSchema.customFields)
				.values(batch)
				.onConflictDoUpdate({
					target: [dbSchema.customFields.apId],
					set: {
						ccId: dbSchema.customFields.ccId,
						name: dbSchema.customFields.name,
						label: dbSchema.customFields.label,
						type: dbSchema.customFields.type,
						apConfig: dbSchema.customFields.apConfig,
						ccConfig: dbSchema.customFields.ccConfig,
						mappingType: dbSchema.customFields.mappingType,
						apStandardField: dbSchema.customFields.apStandardField,
						ccStandardField: dbSchema.customFields.ccStandardField,
						updatedAt: new Date(),
					},
				});
		}

		logDebug("Completed bulk upsert of field mappings", {
			totalMappings: mappings.length,
			batchCount: batches.length,
		});
	} catch (error) {
		const errorMessage = error instanceof Error ? error.message : String(error);
		logError("Failed to bulk upsert field mappings", {
			mappingCount: mappings.length,
			error: errorMessage,
		});
		throw new Error(`Failed to bulk upsert field mappings: ${errorMessage}`);
	}
}

/**
 * Store standard-to-custom field mapping
 *
 * Creates a database record for an AP standard field that maps to a CC custom field.
 * This enables synchronization of AP standard field values to CC custom fields.
 *
 * @param apStandardField - AP standard field name (e.g., "email", "phone")
 * @param ccField - CC custom field that receives the AP standard field value
 * @returns Promise that resolves when mapping is successfully stored
 *
 * @throws {Error} When database operation fails or mapping data is invalid
 *
 * @example
 * ```typescript
 * const ccEmailField: GetCCCustomField = {
 *   id: 123,
 *   name: "email",
 *   label: "Email Address",
 *   type: "email"
 * };
 *
 * await storeStandardToCustomMapping("email", ccEmailField);
 * console.log("Standard-to-custom mapping stored");
 * ```
 *
 * @since 2.0.0
 */
export async function storeStandardToCustomMapping(
	apStandardField: string,
	ccField: GetCCCustomField,
): Promise<void> {
	try {
		const db = getDb();

		const mappingData: CustomFieldInsert = {
			apId: null, // No AP custom field ID for standard fields
			ccId: ccField.id,
			name: apStandardField, // Use AP standard field name
			label: ccField.label,
			type: ccField.type,
			apConfig: null, // No AP custom field config
			ccConfig: ccField,
			mappingType: "standard_to_custom",
			apStandardField: apStandardField,
			ccStandardField: null,
		};

		await db
			.insert(dbSchema.customFields)
			.values(mappingData)
			.onConflictDoUpdate({
				target: [dbSchema.customFields.ccId],
				set: {
					apId: mappingData.apId,
					name: mappingData.name,
					label: mappingData.label,
					type: mappingData.type,
					apConfig: mappingData.apConfig,
					ccConfig: mappingData.ccConfig,
					mappingType: mappingData.mappingType,
					apStandardField: mappingData.apStandardField,
					ccStandardField: mappingData.ccStandardField,
					updatedAt: new Date(),
				},
			});

		logDebug("Stored standard-to-custom field mapping", {
			apStandardField,
			ccFieldId: ccField.id,
			ccFieldName: ccField.name,
		});
	} catch (error) {
		const errorMessage = error instanceof Error ? error.message : String(error);
		logError("Failed to store standard-to-custom field mapping", {
			apStandardField,
			ccFieldId: ccField.id,
			error: errorMessage,
		});
		throw new Error(`Failed to store standard-to-custom field mapping: ${errorMessage}`);
	}
}

/**
 * Get all standard-to-custom field mappings
 *
 * Retrieves all database records where AP standard fields map to CC custom fields.
 * Used for synchronization operations and mapping validation.
 *
 * @returns Promise resolving to array of standard-to-custom mappings
 *
 * @throws {Error} When database query fails
 *
 * @example
 * ```typescript
 * const mappings = await getStandardToCustomMappings();
 * console.log(`Found ${mappings.length} standard-to-custom mappings`);
 * ```
 *
 * @since 2.0.0
 */
export async function getStandardToCustomMappings(): Promise<StandardToCustomMapping[]> {
	try {
		const db = getDb();

		const results = await db
			.select({
				apStandardField: dbSchema.customFields.apStandardField,
				ccCustomField: dbSchema.customFields.name,
				ccFieldId: dbSchema.customFields.ccId,
				ccConfig: dbSchema.customFields.ccConfig,
			})
			.from(dbSchema.customFields)
			.where(eq(dbSchema.customFields.mappingType, "standard_to_custom"));

		const mappings: StandardToCustomMapping[] = results
			.filter((result) => result.apStandardField && result.ccCustomField)
			.map((result) => ({
				apStandardField: result.apStandardField!,
				ccCustomField: result.ccCustomField!,
				ccFieldId: result.ccFieldId!,
				ccConfig: result.ccConfig as GetCCCustomField,
			}));

		logDebug("Retrieved standard-to-custom field mappings", {
			mappingCount: mappings.length,
		});

		return mappings;
	} catch (error) {
		const errorMessage = error instanceof Error ? error.message : String(error);
		logError("Failed to retrieve standard-to-custom field mappings", {
			error: errorMessage,
		});
		throw new Error(`Failed to retrieve standard-to-custom field mappings: ${errorMessage}`);
	}
}

/**
 * Get standard-to-custom mapping for a specific AP standard field
 *
 * @param apStandardField - AP standard field name to find mapping for
 * @returns Promise resolving to mapping if found, null otherwise
 *
 * @throws {Error} When database query fails
 *
 * @since 2.0.0
 */
export async function getStandardToCustomMapping(
	apStandardField: string,
): Promise<StandardToCustomMapping | null> {
	try {
		const db = getDb();

		const result = await db
			.select({
				apStandardField: dbSchema.customFields.apStandardField,
				ccCustomField: dbSchema.customFields.name,
				ccFieldId: dbSchema.customFields.ccId,
				ccConfig: dbSchema.customFields.ccConfig,
			})
			.from(dbSchema.customFields)
			.where(
				and(
					eq(dbSchema.customFields.mappingType, "standard_to_custom"),
					eq(dbSchema.customFields.apStandardField, apStandardField)
				)
			)
			.limit(1);

		if (result.length === 0 || !result[0].apStandardField || !result[0].ccCustomField) {
			return null;
		}

		const mapping: StandardToCustomMapping = {
			apStandardField: result[0].apStandardField,
			ccCustomField: result[0].ccCustomField,
			ccFieldId: result[0].ccFieldId!,
			ccConfig: result[0].ccConfig as GetCCCustomField,
		};

		logDebug("Retrieved standard-to-custom field mapping", {
			apStandardField,
			ccCustomField: mapping.ccCustomField,
		});

		return mapping;
	} catch (error) {
		const errorMessage = error instanceof Error ? error.message : String(error);
		logError("Failed to retrieve standard-to-custom field mapping", {
			apStandardField,
			error: errorMessage,
		});
		throw new Error(`Failed to retrieve standard-to-custom field mapping: ${errorMessage}`);
	}
}
