/**
 * Standard Field Auto-Discovery
 *
 * Provides intelligent auto-discovery of standard-to-custom field mappings
 * by analyzing CliniCore custom fields and matching them to AutoPatient
 * standard fields based on name similarity, type compatibility, and
 * semantic analysis.
 *
 * @fileoverview Auto-discovery utilities for standard field mappings
 * @version 1.0.0
 * @since 2024-08-04
 */

import type { GetCCCustomField } from "@type";
import { AP_MAPPABLE_STANDARD_FIELDS } from "@/config/standardFieldMappings";
import { logDebug, logInfo } from "@/utils/logger";
import type { StandardToCustomMapping } from "@/config/standardFieldMappings";

/**
 * Auto-discovery result for a single field
 */
export interface FieldDiscoveryResult {
	/** CC custom field that was analyzed */
	ccField: GetCCCustomField;
	/** Matched AP standard field name (if any) */
	apStandardField?: string;
	/** Confidence score (0-1) */
	confidence: number;
	/** Reasons for the match or rejection */
	reasons: string[];
	/** Whether this field should be mapped */
	shouldMap: boolean;
}

/**
 * Auto-discovery result for all fields
 */
export interface AutoDiscoveryResult {
	/** Suggested mappings with high confidence */
	suggestedMappings: StandardToCustomMapping[];
	/** All field analysis results */
	fieldResults: FieldDiscoveryResult[];
	/** Overall discovery statistics */
	statistics: {
		totalCcFields: number;
		fieldsAnalyzed: number;
		highConfidenceMatches: number;
		mediumConfidenceMatches: number;
		lowConfidenceMatches: number;
		noMatches: number;
	};
}

/**
 * Configuration for auto-discovery behavior
 */
export interface DiscoveryConfig {
	/** Minimum confidence threshold for suggestions (0-1) */
	confidenceThreshold: number;
	/** Whether to include medium confidence matches */
	includeMediumConfidence: boolean;
	/** Whether to analyze field descriptions/labels */
	analyzeDescriptions: boolean;
	/** Custom field name patterns to exclude */
	excludePatterns?: string[];
}

/**
 * Default discovery configuration
 */
const DEFAULT_CONFIG: DiscoveryConfig = {
	confidenceThreshold: 0.7,
	includeMediumConfidence: true,
	analyzeDescriptions: true,
	excludePatterns: [
		"^(test_|dev_|debug_)",
		"^(internal_|system_)",
		"^(temp_|tmp_)",
	],
};

/**
 * Discover standard-to-custom field mappings automatically
 *
 * Analyzes CliniCore custom fields to identify potential mappings to
 * AutoPatient standard fields. Uses intelligent matching algorithms
 * based on field names, types, and semantic analysis.
 *
 * @param ccFields - Array of CliniCore custom fields to analyze
 * @param config - Discovery configuration options
 * @returns Auto-discovery result with suggested mappings
 *
 * @example
 * ```typescript
 * const ccFields = await apiClient.cc.ccCustomfieldReq.all();
 * const result = await discoverStandardToCustomMappings(ccFields);
 * 
 * console.log(`Found ${result.suggestedMappings.length} suggested mappings`);
 * for (const mapping of result.suggestedMappings) {
 *   console.log(`${mapping.apStandardField} -> ${mapping.ccCustomField}`);
 * }
 * ```
 *
 * @since 1.0.0
 */
export async function discoverStandardToCustomMappings(
	ccFields: GetCCCustomField[],
	config: Partial<DiscoveryConfig> = {},
): Promise<AutoDiscoveryResult> {
	const finalConfig = { ...DEFAULT_CONFIG, ...config };
	
	logInfo("Starting auto-discovery of standard-to-custom field mappings", {
		totalCcFields: ccFields.length,
		confidenceThreshold: finalConfig.confidenceThreshold,
	});

	const result: AutoDiscoveryResult = {
		suggestedMappings: [],
		fieldResults: [],
		statistics: {
			totalCcFields: ccFields.length,
			fieldsAnalyzed: 0,
			highConfidenceMatches: 0,
			mediumConfidenceMatches: 0,
			lowConfidenceMatches: 0,
			noMatches: 0,
		},
	};

	// Filter out excluded fields
	const fieldsToAnalyze = ccFields.filter(field => 
		!isFieldExcluded(field, finalConfig.excludePatterns || [])
	);

	logDebug(`Analyzing ${fieldsToAnalyze.length} CC fields after exclusions`);

	// Analyze each CC field
	for (const ccField of fieldsToAnalyze) {
		const fieldResult = analyzeFieldForStandardMapping(ccField, finalConfig);
		result.fieldResults.push(fieldResult);
		result.statistics.fieldsAnalyzed++;

		// Categorize by confidence
		if (fieldResult.confidence >= 0.8) {
			result.statistics.highConfidenceMatches++;
		} else if (fieldResult.confidence >= 0.5) {
			result.statistics.mediumConfidenceMatches++;
		} else if (fieldResult.confidence > 0) {
			result.statistics.lowConfidenceMatches++;
		} else {
			result.statistics.noMatches++;
		}

		// Add to suggested mappings if meets criteria
		if (fieldResult.shouldMap && fieldResult.apStandardField) {
			const mapping: StandardToCustomMapping = {
				apStandardField: fieldResult.apStandardField,
				ccCustomField: ccField.name,
				ccFieldId: ccField.id,
				ccConfig: ccField,
				notes: `Auto-discovered with ${(fieldResult.confidence * 100).toFixed(1)}% confidence: ${fieldResult.reasons.join(", ")}`,
			};
			result.suggestedMappings.push(mapping);
		}
	}

	logInfo("Auto-discovery completed", {
		fieldsAnalyzed: result.statistics.fieldsAnalyzed,
		suggestedMappings: result.suggestedMappings.length,
		highConfidence: result.statistics.highConfidenceMatches,
		mediumConfidence: result.statistics.mediumConfidenceMatches,
	});

	return result;
}

/**
 * Analyze a single CC field for potential standard field mapping
 *
 * @param ccField - CliniCore custom field to analyze
 * @param config - Discovery configuration
 * @returns Field discovery result
 */
function analyzeFieldForStandardMapping(
	ccField: GetCCCustomField,
	config: DiscoveryConfig,
): FieldDiscoveryResult {
	const result: FieldDiscoveryResult = {
		ccField,
		confidence: 0,
		reasons: [],
		shouldMap: false,
	};

	// Try to match against each AP standard field
	let bestMatch: { field: string; score: number; reasons: string[] } | null = null;

	for (const apStandardField of AP_MAPPABLE_STANDARD_FIELDS) {
		const matchResult = calculateFieldMatchScore(
			ccField,
			apStandardField,
			config,
		);

		if (matchResult.score > (bestMatch?.score || 0)) {
			bestMatch = {
				field: apStandardField,
				score: matchResult.score,
				reasons: matchResult.reasons,
			};
		}
	}

	if (bestMatch && bestMatch.score >= config.confidenceThreshold) {
		result.apStandardField = bestMatch.field;
		result.confidence = bestMatch.score;
		result.reasons = bestMatch.reasons;
		result.shouldMap = true;
	} else if (bestMatch) {
		result.apStandardField = bestMatch.field;
		result.confidence = bestMatch.score;
		result.reasons = [...bestMatch.reasons, "Below confidence threshold"];
		result.shouldMap = false;
	} else {
		result.reasons = ["No suitable AP standard field match found"];
		result.shouldMap = false;
	}

	return result;
}

/**
 * Calculate match score between CC field and AP standard field
 *
 * @param ccField - CliniCore custom field
 * @param apStandardField - AutoPatient standard field name
 * @param config - Discovery configuration
 * @returns Match score and reasons
 */
function calculateFieldMatchScore(
	ccField: GetCCCustomField,
	apStandardField: string,
	config: DiscoveryConfig,
): { score: number; reasons: string[] } {
	const reasons: string[] = [];
	let score = 0;

	// Exact name match (highest score)
	if (ccField.name.toLowerCase() === apStandardField.toLowerCase()) {
		score += 0.9;
		reasons.push("Exact name match");
	}
	// Close name match
	else if (isCloseNameMatch(ccField.name, apStandardField)) {
		score += 0.7;
		reasons.push("Close name match");
	}
	// Partial name match
	else if (isPartialNameMatch(ccField.name, apStandardField)) {
		score += 0.4;
		reasons.push("Partial name match");
	}

	// Label/description analysis (if enabled)
	if (config.analyzeDescriptions && ccField.label) {
		const labelScore = analyzeLabelMatch(ccField.label, apStandardField);
		if (labelScore > 0) {
			score += labelScore * 0.3; // Weight label matches less than name matches
			reasons.push(`Label similarity (${(labelScore * 100).toFixed(1)}%)`);
		}
	}

	// Type compatibility
	const typeScore = analyzeTypeCompatibility(ccField.type, apStandardField);
	if (typeScore > 0) {
		score += typeScore * 0.2;
		reasons.push(`Type compatibility (${(typeScore * 100).toFixed(1)}%)`);
	}

	// Normalize score to 0-1 range
	score = Math.min(score, 1.0);

	return { score, reasons };
}

/**
 * Check if field should be excluded from analysis
 */
function isFieldExcluded(field: GetCCCustomField, excludePatterns: string[]): boolean {
	for (const pattern of excludePatterns) {
		try {
			const regex = new RegExp(pattern, "i");
			if (regex.test(field.name) || (field.label && regex.test(field.label))) {
				return true;
			}
		} catch {
			// Invalid regex, skip
		}
	}
	return false;
}

/**
 * Check for close name matches (handles common variations)
 */
function isCloseNameMatch(ccName: string, apField: string): boolean {
	const ccLower = ccName.toLowerCase();
	const apLower = apField.toLowerCase();

	// Common variations
	const variations = [
		[ccLower, apLower.replace(/([A-Z])/g, "_$1").toLowerCase()], // camelCase to snake_case
		[ccLower.replace(/_/g, ""), apLower], // Remove underscores
		[ccLower.replace(/-/g, ""), apLower], // Remove hyphens
		[ccLower, apLower.replace(/([A-Z])/g, "-$1").toLowerCase()], // camelCase to kebab-case
	];

	return variations.some(([cc, ap]) => cc === ap);
}

/**
 * Check for partial name matches
 */
function isPartialNameMatch(ccName: string, apField: string): boolean {
	const ccLower = ccName.toLowerCase();
	const apLower = apField.toLowerCase();

	return ccLower.includes(apLower) || apLower.includes(ccLower);
}

/**
 * Analyze label for semantic similarity
 */
function analyzeLabelMatch(label: string, apField: string): number {
	const labelLower = label.toLowerCase();
	const apLower = apField.toLowerCase();

	// Simple keyword matching for common fields
	const fieldKeywords: Record<string, string[]> = {
		email: ["email", "e-mail", "electronic mail", "contact email"],
		phone: ["phone", "telephone", "mobile", "cell", "contact number"],
		firstName: ["first name", "given name", "forename"],
		lastName: ["last name", "surname", "family name"],
		dateOfBirth: ["date of birth", "birth date", "dob", "birthday"],
		gender: ["gender", "sex"],
		address1: ["address", "street", "street address"],
		city: ["city", "town"],
		state: ["state", "province", "region"],
		country: ["country", "nation"],
		postalCode: ["postal code", "zip code", "zip", "postcode"],
	};

	const keywords = fieldKeywords[apField] || [];
	for (const keyword of keywords) {
		if (labelLower.includes(keyword)) {
			return 0.8; // High confidence for keyword matches
		}
	}

	return 0;
}

/**
 * Analyze type compatibility between CC field type and AP standard field
 */
function analyzeTypeCompatibility(ccType: string, apField: string): number {
	// Define expected types for AP standard fields
	const expectedTypes: Record<string, string[]> = {
		email: ["email", "text"],
		phone: ["telephone", "text"],
		firstName: ["text"],
		lastName: ["text"],
		dateOfBirth: ["date"],
		gender: ["select", "text"],
		address1: ["text", "textarea"],
		city: ["text"],
		state: ["text", "select"],
		country: ["text", "select"],
		postalCode: ["text"],
	};

	const expected = expectedTypes[apField] || ["text"];
	return expected.includes(ccType) ? 1.0 : 0.5; // Partial compatibility for unexpected types
}
