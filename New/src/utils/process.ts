import { APContactCreationWebhookPayload } from "@/processors/apWebhook";
import { CCWebhookPayload } from "@/processors/ccWebhook";
import { useKV } from "./kv";

type ProcessCTX = APContactCreationWebhookPayload | CCWebhookPayload;

class ProcessKV {
    private kv = useKV();
    private prefix = "process:";

   private getPlatform(ctx: ProcessCTX): "ap" | "cc" {
        if (typeof ctx === "object" && "event" in ctx) {
            return "cc";
        } else {
            return "ap";
        }
    }

    private addCCPatient() {}
    private addAPContact() {}
    private addCCAppointment() {}
    private addAPAppointment() {}

    

    async addToProcess(ctx: ProcessCTX) {
        if (typeof ctx === "object" && "event" in ctx) {
            // CC Webhook
        } else {
            // AP Webhook
        }
    }

    async isInProcess(ctx: ProcessCTX) {}

    async removeFromProcess(ctx: ProcessCTX) {}

    async shouldProcess(ctx: ProcessCTX){}
}

const kv = new ProcessKV();

export default kv as ProcessKV;
