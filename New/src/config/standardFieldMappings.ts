/**
 * Standard Field Mappings Configuration
 *
 * Defines the mapping between standard fields on one platform and custom fields
 * on another platform. This prevents the system from attempting to create
 * duplicate custom fields when a field should map to an existing standard field.
 *
 * **Key Concepts:**
 * - AP Standard Fields: Built-in contact fields in AutoPatient (email, phone, firstName, etc.)
 * - CC Standard Fields: Built-in patient fields in CliniCore (firstName, lastName, email, etc.)
 * - Cross-Platform Mappings: When a custom field on one platform corresponds to a standard field on another
 * - Creation Blocklist: AP custom fields that should never be created as CC custom fields
 *
 * **Usage:**
 * - Prevents creation of AP custom field "phone" when CC custom field "phone" should map to AP standard field "phone"
 * - Prevents creation of CC custom field "source" when AP custom field "source" should map to CC standard field (if any)
 * - Blocks creation of AP fields that are internal/system-specific and shouldn't exist in CC
 */

/**
 * AutoPatient standard contact fields
 * These are built-in fields that cannot be created as custom fields
 */
export const AP_STANDARD_FIELDS = new Set([
	// Core contact information
	"email",
	"phone",
	"firstName",
	"lastName",
	"name",
	"companyName",

	// Address fields
	"address1",
	"address",
	"city",
	"state",
	"country",
	"postalCode",
	"zipCode",

	// Contact metadata
	"source",
	"timezone",
	"dateOfBirth",
	"dob",
	"ssn",
	"gender",
	"assignedTo",
	"tags",
	"dnd",

	// System fields
	"id",
	"locationId",
	"emailLowerCase",
	"createdAt",
	"updatedAt",
]);

/**
 * CliniCore standard patient fields
 * These are built-in fields that cannot be created as custom fields
 */
export const CC_STANDARD_FIELDS = new Set([
	// Core patient information
	"firstName",
	"lastName",
	"email",
	"phoneMobile",
	"phone", // Often stored as custom field but maps to phoneMobile

	// Patient metadata
	"dob",
	"dateOfBirth",
	"ssn",
	"gender",
	"active",
	"title",
	"titleSuffix",
	"healthInsurance",

	// System fields
	"id",
	"createdAt",
	"updatedAt",
	"createdBy",
	"updatedBy",
	"flashMessage",
	"qrUrl",
	"avatarUrl",
]);

/**
 * Blocklist entry interface for field creation prevention
 */
export interface BlocklistEntry {
	/** Field name pattern to block (exact match or regex pattern) */
	pattern: string;
	/** Whether this is a regex pattern (true) or exact match (false) */
	isRegex: boolean;
	/** Case-sensitive matching (default: false) */
	caseSensitive?: boolean;
	/** Reason why this field is blocked */
	reason: string;
}

/**
 * AutoPatient to CliniCore Creation Blocklist
 *
 * Prevents specific AutoPatient custom fields from being created as CliniCore custom fields.
 * This is useful for blocking internal system fields, AP-specific fields that don't translate
 * to CC, or fields that would cause conflicts or confusion in the target system.
 *
 * **Pattern Matching:**
 * - Exact matches: Set `isRegex: false` for precise field name blocking
 * - Regex patterns: Set `isRegex: true` for pattern-based blocking (e.g., fields containing keywords)
 * - Case sensitivity: Controlled by `caseSensitive` flag (default: false)
 *
 * **Common Blocklist Categories:**
 * - Internal system fields (IDs, timestamps, metadata)
 * - AP-specific workflow fields that don't apply to CC
 * - Legacy or deprecated fields
 * - Fields with naming conflicts or ambiguous meanings
 */
export const AP_TO_CC_CREATION_BLOCKLIST: BlocklistEntry[] = [
	// Internal system fields
	{
		pattern: "^(id|_id|internal_id)$",
		isRegex: true,
		caseSensitive: false,
		reason: "Internal system identifier fields should not be replicated",
	},
	{
		pattern: "^(created_at|updated_at|timestamp|last_modified)$",
		isRegex: true,
		caseSensitive: false,
		reason: "System timestamp fields are platform-specific",
	},
	{
		pattern: "^(location_id|locationId|clinic_id|clinicId)$",
		isRegex: true,
		caseSensitive: false,
		reason: "Location/clinic identifiers are platform-specific",
	},

	// AP-specific workflow fields
	{
		pattern: "pipeline",
		isRegex: false,
		caseSensitive: false,
		reason: "AutoPatient pipeline fields don't translate to CliniCore workflow",
	},
	{
		pattern: "funnel",
		isRegex: false,
		caseSensitive: false,
		reason: "AutoPatient funnel fields don't translate to CliniCore workflow",
	},
	{
		pattern: "lead_score",
		isRegex: false,
		caseSensitive: false,
		reason: "Lead scoring is AutoPatient-specific marketing concept",
	},
	{
		pattern: "conversion_tracking",
		isRegex: false,
		caseSensitive: false,
		reason: "Conversion tracking is AutoPatient-specific marketing concept",
	},

	// Communication preferences (AP-specific)
	{
		pattern: "^(dnd|do_not_disturb|email_opt_out|sms_opt_out)$",
		isRegex: true,
		caseSensitive: false,
		reason:
			"Communication preferences are handled differently in each platform",
	},

	// Legacy or deprecated fields
	{
		pattern: "^(legacy_|deprecated_|old_)",
		isRegex: true,
		caseSensitive: false,
		reason: "Legacy and deprecated fields should not be propagated",
	},

	// Test and development fields
	{
		pattern: "^(test_|dev_|debug_|temp_)",
		isRegex: true,
		caseSensitive: false,
		reason:
			"Test and development fields should not be created in production systems",
	},

	// Platform-specific integrations
	{
		pattern: "^(zapier_|webhook_|api_key|integration_)",
		isRegex: true,
		caseSensitive: false,
		reason: "Integration-specific fields are platform-dependent",
	},
];

/**
 * Field mapping interface for cross-platform standard field mappings
 */
export interface StandardFieldMapping {
	/** Source field name (custom field name on source platform) */
	sourceField: string;
	/** Target field name (standard field name on target platform) */
	targetField: string;
	/** Source platform where this is a custom field */
	sourcePlatform: "ap" | "cc";
	/** Target platform where this is a standard field */
	targetPlatform: "ap" | "cc";
	/** Optional field transformation notes */
	notes?: string;
}

/**
 * Cross-platform standard field mappings
 * Maps custom fields on one platform to standard fields on another
 */
export const STANDARD_FIELD_MAPPINGS: StandardFieldMapping[] = [
	// CC custom fields → AP standard fields
	{
		sourceField: "phone",
		targetField: "phone",
		sourcePlatform: "cc",
		targetPlatform: "ap",
		notes: "CC custom field 'phone' maps to AP standard field 'phone'",
	},
	{
		sourceField: "phone-mobile",
		targetField: "phone",
		sourcePlatform: "cc",
		targetPlatform: "ap",
		notes: "CC custom field 'phone-mobile' maps to AP standard field 'phone'",
	},
	{
		sourceField: "phoneMobile",
		targetField: "phone",
		sourcePlatform: "cc",
		targetPlatform: "ap",
		notes: "CC custom field 'phoneMobile' maps to AP standard field 'phone'",
	},
	{
		sourceField: "email",
		targetField: "email",
		sourcePlatform: "cc",
		targetPlatform: "ap",
		notes: "CC custom field 'email' maps to AP standard field 'email'",
	},
	{
		sourceField: "source",
		targetField: "source",
		sourcePlatform: "cc",
		targetPlatform: "ap",
		notes: "CC custom field 'source' maps to AP standard field 'source'",
	},
	{
		sourceField: "gender",
		targetField: "gender",
		sourcePlatform: "cc",
		targetPlatform: "ap",
		notes: "CC custom field 'gender' maps to AP standard field 'gender'",
	},
	{
		sourceField: "dateOfBirth",
		targetField: "dateOfBirth",
		sourcePlatform: "cc",
		targetPlatform: "ap",
		notes:
			"CC custom field 'dateOfBirth' maps to AP standard field 'dateOfBirth'",
	},
	{
		sourceField: "dob",
		targetField: "dateOfBirth",
		sourcePlatform: "cc",
		targetPlatform: "ap",
		notes: "CC custom field 'dob' maps to AP standard field 'dateOfBirth'",
	},

	// AP custom fields → CC standard fields
	{
		sourceField: "firstName",
		targetField: "firstName",
		sourcePlatform: "ap",
		targetPlatform: "cc",
		notes: "AP custom field 'firstName' maps to CC standard field 'firstName'",
	},
	{
		sourceField: "lastName",
		targetField: "lastName",
		sourcePlatform: "ap",
		targetPlatform: "cc",
		notes: "AP custom field 'lastName' maps to CC standard field 'lastName'",
	},
	{
		sourceField: "email",
		targetField: "email",
		sourcePlatform: "ap",
		targetPlatform: "cc",
		notes: "AP custom field 'email' maps to CC standard field 'email'",
	},
	{
		sourceField: "phone",
		targetField: "phoneMobile",
		sourcePlatform: "ap",
		targetPlatform: "cc",
		notes: "AP custom field 'phone' maps to CC standard field 'phoneMobile'",
	},
	{
		sourceField: "gender",
		targetField: "gender",
		sourcePlatform: "ap",
		targetPlatform: "cc",
		notes: "AP custom field 'gender' maps to CC standard field 'gender'",
	},
	{
		sourceField: "dateOfBirth",
		targetField: "dob",
		sourcePlatform: "ap",
		targetPlatform: "cc",
		notes: "AP custom field 'dateOfBirth' maps to CC standard field 'dob'",
	},
	{
		sourceField: "dob",
		targetField: "dob",
		sourcePlatform: "ap",
		targetPlatform: "cc",
		notes: "AP custom field 'dob' maps to CC standard field 'dob'",
	},
];

/**
 * Standard-to-custom field mapping interface
 * For mapping AP standard fields to CC custom fields
 */
export interface StandardToCustomMapping {
	/** AP standard field name (e.g., "email", "phone") */
	apStandardField: string;
	/** Target CC custom field name */
	ccCustomField: string;
	/** CC custom field ID (populated from database) */
	ccFieldId?: number;
	/** CC custom field configuration (populated from database) */
	ccConfig?: import("@type").GetCCCustomField;
	/** Optional field transformation notes */
	notes?: string;
}

/**
 * AP standard fields to CC custom fields mappings
 * Maps AP standard fields to their corresponding CC custom fields
 */
export const AP_STANDARD_TO_CC_CUSTOM_MAPPINGS: StandardToCustomMapping[] = [
	{
		apStandardField: "email",
		ccCustomField: "email",
		notes: "AP standard field 'email' maps to CC custom field 'email'",
	},
	{
		apStandardField: "phone",
		ccCustomField: "phone",
		notes: "AP standard field 'phone' maps to CC custom field 'phone'",
	},
	{
		apStandardField: "firstName",
		ccCustomField: "firstName",
		notes: "AP standard field 'firstName' maps to CC custom field 'firstName'",
	},
	{
		apStandardField: "lastName",
		ccCustomField: "lastName",
		notes: "AP standard field 'lastName' maps to CC custom field 'lastName'",
	},
	{
		apStandardField: "dateOfBirth",
		ccCustomField: "dateOfBirth",
		notes: "AP standard field 'dateOfBirth' maps to CC custom field 'dateOfBirth'",
	},
	{
		apStandardField: "gender",
		ccCustomField: "gender",
		notes: "AP standard field 'gender' maps to CC custom field 'gender'",
	},
	{
		apStandardField: "source",
		ccCustomField: "source",
		notes: "AP standard field 'source' maps to CC custom field 'source'",
	},
];

/**
 * List of AutoPatient standard fields that can be mapped to CC custom fields
 * This is a subset of AP_STANDARD_FIELDS containing only fields suitable for mapping
 */
export const AP_MAPPABLE_STANDARD_FIELDS = [
	"email",
	"phone",
	"firstName",
	"lastName",
	"name",
	"dateOfBirth",
	"gender",
	"source",
	"country",
	"address1",
	"city",
	"state",
	"postalCode",
	"companyName",
	"tags",
] as const;

/**
 * Check if a field name conflicts with standard fields on the target platform
 *
 * @param fieldName - Field name to check
 * @param targetPlatform - Platform to check against ("ap" | "cc")
 * @returns True if the field name conflicts with a standard field
 */
export function isStandardFieldConflict(
	fieldName: string,
	targetPlatform: "ap" | "cc",
): boolean {
	const standardFields =
		targetPlatform === "ap" ? AP_STANDARD_FIELDS : CC_STANDARD_FIELDS;
	return standardFields.has(fieldName.toLowerCase());
}

/**
 * Find standard field mapping for a given field
 *
 * @param fieldName - Source field name to find mapping for
 * @param sourcePlatform - Platform where this is a custom field
 * @param targetPlatform - Platform where this should map to a standard field
 * @returns Standard field mapping if found, null otherwise
 */
export function findStandardFieldMapping(
	fieldName: string,
	sourcePlatform: "ap" | "cc",
	targetPlatform: "ap" | "cc",
): StandardFieldMapping | null {
	return (
		STANDARD_FIELD_MAPPINGS.find(
			(mapping) =>
				mapping.sourceField.toLowerCase() === fieldName.toLowerCase() &&
				mapping.sourcePlatform === sourcePlatform &&
				mapping.targetPlatform === targetPlatform,
		) || null
	);
}

/**
 * Get all standard field mappings for a specific platform direction
 *
 * @param sourcePlatform - Source platform
 * @param targetPlatform - Target platform
 * @returns Array of standard field mappings
 */
export function getStandardFieldMappings(
	sourcePlatform: "ap" | "cc",
	targetPlatform: "ap" | "cc",
): StandardFieldMapping[] {
	return STANDARD_FIELD_MAPPINGS.filter(
		(mapping) =>
			mapping.sourcePlatform === sourcePlatform &&
			mapping.targetPlatform === targetPlatform,
	);
}

/**
 * Check if an AutoPatient field name is blocked from CC creation
 *
 * @param fieldName - AP field name to check against blocklist
 * @returns Blocklist entry if field is blocked, null if allowed
 */
export function checkApToCcCreationBlocklist(
	fieldName: string,
): BlocklistEntry | null {
	for (const entry of AP_TO_CC_CREATION_BLOCKLIST) {
		const testName = entry.caseSensitive ? fieldName : fieldName.toLowerCase();
		const testPattern = entry.caseSensitive
			? entry.pattern
			: entry.pattern.toLowerCase();

		if (entry.isRegex) {
			try {
				const regex = new RegExp(testPattern, entry.caseSensitive ? "g" : "gi");
				if (regex.test(testName)) {
					return entry;
				}
			} catch (error) {
				// Invalid regex pattern, skip this entry
				console.warn(
					`Invalid regex pattern in blocklist: ${entry.pattern}`,
					error,
				);
			}
		} else {
			// Exact match
			if (testName === testPattern) {
				return entry;
			}
		}
	}

	return null;
}

/**
 * Get all blocked field names from a list of AP fields
 *
 * @param apFields - Array of AP fields to check
 * @returns Array of objects containing the field and the blocklist entry that blocked it
 */
export function getBlockedApFields(apFields: { name: string }[]): Array<{
	field: { name: string };
	blocklistEntry: BlocklistEntry;
}> {
	const blockedFields: Array<{
		field: { name: string };
		blocklistEntry: BlocklistEntry;
	}> = [];

	for (const field of apFields) {
		const blocklistEntry = checkApToCcCreationBlocklist(field.name);
		if (blocklistEntry) {
			blockedFields.push({ field, blocklistEntry });
		}
	}

	return blockedFields;
}

/**
 * Find standard-to-custom field mapping for a given AP standard field
 *
 * @param apStandardField - AP standard field name to find mapping for
 * @returns Standard-to-custom field mapping if found, null otherwise
 */
export function findStandardToCustomMapping(
	apStandardField: string,
): StandardToCustomMapping | null {
	return (
		AP_STANDARD_TO_CC_CUSTOM_MAPPINGS.find(
			(mapping) =>
				mapping.apStandardField.toLowerCase() === apStandardField.toLowerCase(),
		) || null
	);
}

/**
 * Get all AP standard fields that have custom field mappings
 *
 * @returns Array of AP standard field names that map to CC custom fields
 */
export function getApStandardFieldsWithCustomMappings(): string[] {
	return AP_STANDARD_TO_CC_CUSTOM_MAPPINGS.map(
		(mapping) => mapping.apStandardField,
	);
}

/**
 * Check if an AP standard field has a custom field mapping
 *
 * @param apStandardField - AP standard field name to check
 * @returns True if the field has a custom field mapping, false otherwise
 */
export function hasStandardToCustomMapping(apStandardField: string): boolean {
	return findStandardToCustomMapping(apStandardField) !== null;
}

/**
 * Get all standard-to-custom mappings
 *
 * @returns Array of all standard-to-custom field mappings
 */
export function getAllStandardToCustomMappings(): StandardToCustomMapping[] {
	return [...AP_STANDARD_TO_CC_CUSTOM_MAPPINGS];
}
